class FileHandler {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.setupFileHandlers();
    }

    setupFileHandlers() {
        // Image upload handler
        const imageUpload = document.getElementById('imageUpload');
        imageUpload.addEventListener('change', (e) => this.handleImageUpload(e));

        // Export handler
        const exportBtn = document.getElementById('exportFlyer');
        exportBtn.addEventListener('click', () => this.exportFlyer());

        // Save project handler
        const saveBtn = document.getElementById('saveProject');
        saveBtn.addEventListener('click', () => this.saveProject());

        // Load project handlers
        const loadBtn = document.getElementById('loadProjectBtn');
        const loadInput = document.getElementById('loadProject');
        
        loadBtn.addEventListener('click', () => loadInput.click());
        loadInput.addEventListener('change', (e) => this.handleProjectLoad(e));

        // Clear image handler
        const clearBtn = document.getElementById('clearImage');
        clearBtn.addEventListener('click', () => this.clearImage());
    }

    handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            return;
        }

        // Check file size (limit to 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('Image file is too large. Please select an image smaller than 10MB.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.canvasManager.setBackgroundImage(img);
                console.log('Background image loaded successfully');
            };
            img.onerror = () => {
                alert('Failed to load the image. Please try a different file.');
            };
            img.src = e.target.result;
        };
        reader.onerror = () => {
            alert('Failed to read the image file.');
        };
        reader.readAsDataURL(file);
    }

    clearImage() {
        this.canvasManager.clearBackgroundImage();
        document.getElementById('imageUpload').value = '';
        console.log('Background image cleared');
    }

    exportFlyer() {
        try {
            const dataURL = this.canvasManager.exportAsImage('png', 1.0);
            
            // Create download link
            const link = document.createElement('a');
            link.download = `flyer-${this.generateTimestamp()}.png`;
            link.href = dataURL;
            
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log('Flyer exported successfully');
        } catch (error) {
            console.error('Export failed:', error);
            alert('Failed to export flyer. Please try again.');
        }
    }

    saveProject() {
        try {
            const projectData = this.canvasManager.getProjectData();
            const dataStr = JSON.stringify(projectData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            // Create download link
            const link = document.createElement('a');
            link.download = `flyer-project-${this.generateTimestamp()}.json`;
            link.href = URL.createObjectURL(dataBlob);
            
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Clean up
            URL.revokeObjectURL(link.href);
            
            console.log('Project saved successfully');
        } catch (error) {
            console.error('Save failed:', error);
            alert('Failed to save project. Please try again.');
        }
    }

    handleProjectLoad(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type === 'application/json' && !file.name.endsWith('.json')) {
            alert('Please select a valid JSON project file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const projectData = JSON.parse(e.target.result);
                this.loadProject(projectData);
                console.log('Project loaded successfully');
            } catch (error) {
                console.error('Load failed:', error);
                alert('Failed to load project. The file may be corrupted or invalid.');
            }
        };
        reader.onerror = () => {
            alert('Failed to read the project file.');
        };
        reader.readAsText(file);
    }

    loadProject(projectData) {
        try {
            // Load the project data into canvas manager
            this.canvasManager.loadProjectData(projectData);
            
            // Update UI controls with loaded data
            if (projectData.textElements && projectData.textElements.length >= 3) {
                const elements = projectData.textElements;
                
                // Update band name
                if (elements[0]) {
                    document.getElementById('bandName').value = elements[0].text || '';
                    document.getElementById('bandNameBg').value = elements[0].backgroundColor || '#000000';
                    document.getElementById('bandNameSize').value = elements[0].fontSize || 36;
                    document.querySelector('#bandNameSize + .size-display').textContent = `${elements[0].fontSize || 36}px`;
                }
                
                // Update address
                if (elements[1]) {
                    document.getElementById('address').value = elements[1].text || '';
                    document.getElementById('addressBg').value = elements[1].backgroundColor || '#000000';
                    document.getElementById('addressSize').value = elements[1].fontSize || 24;
                    document.querySelector('#addressSize + .size-display').textContent = `${elements[1].fontSize || 24}px`;
                }
                
                // Update time
                if (elements[2]) {
                    document.getElementById('time').value = elements[2].text || '';
                    document.getElementById('timeBg').value = elements[2].backgroundColor || '#000000';
                    document.getElementById('timeSize').value = elements[2].fontSize || 20;
                    document.querySelector('#timeSize + .size-display').textContent = `${elements[2].fontSize || 20}px`;
                }
            }
            
        } catch (error) {
            console.error('Error loading project:', error);
            alert('Failed to load project data. Please check the file format.');
        }
    }

    generateTimestamp() {
        const now = new Date();
        return now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
    }

    // Utility method to validate image files
    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return validTypes.includes(file.type);
    }

    // Utility method to get file size in human readable format
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
