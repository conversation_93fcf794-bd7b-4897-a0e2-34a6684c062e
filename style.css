* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.main-content {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.controls-panel {
    flex: 0 0 300px;
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.control-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.control-group:last-child {
    border-bottom: none;
}

.control-group h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.text-control {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.text-control label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.text-control input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 14px;
}

.text-control input[type="color"] {
    width: 40px;
    height: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.text-control input[type="range"] {
    width: 120px;
    margin-right: 10px;
}

.size-display {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

button {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
    margin-bottom: 10px;
    transition: background-color 0.3s;
}

button:hover {
    background: #2980b9;
}

button:active {
    transform: translateY(1px);
}

#clearImage {
    background: #e74c3c;
}

#clearImage:hover {
    background: #c0392b;
}

#exportFlyer {
    background: #27ae60;
}

#exportFlyer:hover {
    background: #229954;
}

#saveProject, #loadProjectBtn {
    background: #f39c12;
}

#saveProject:hover, #loadProjectBtn:hover {
    background: #e67e22;
}

input[type="file"] {
    margin-bottom: 10px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#flyerCanvas {
    border: 2px solid #34495e;
    border-radius: 8px;
    background: white;
    cursor: crosshair;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

#flyerCanvas:hover {
    cursor: grab;
}

#flyerCanvas.dragging {
    cursor: grabbing;
}

.canvas-info {
    margin-top: 15px;
    text-align: center;
    color: #7f8c8d;
    font-size: 14px;
}

.canvas-info p {
    background: white;
    padding: 10px 20px;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Responsive design */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .controls-panel {
        flex: none;
        width: 100%;
        max-width: 600px;
        margin: 0 auto 20px;
    }
}

@media (max-width: 600px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .controls-panel {
        padding: 15px;
    }
    
    #flyerCanvas {
        max-width: 100%;
        height: auto;
    }
}
