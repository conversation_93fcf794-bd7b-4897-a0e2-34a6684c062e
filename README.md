# Flyer Creator

A web-based application for creating professional flyers with custom text and background images.

## Features

- **Canvas Size**: 900px wide × 500px high (perfect for flyers)
- **Background Images**: Upload and display background images
- **Draggable Text**: Three text elements (Band Name, Address, Time) that can be moved around with mouse/touch
- **Customizable Text**: 
  - Adjustable font sizes (12px - 72px)
  - Background colors for text legibility
  - Real-time text editing
- **Export**: Save flyers as PNG images
- **Project Management**: Save and load project files for future editing
- **Responsive Design**: Works on desktop and mobile devices

## How to Use

### Getting Started
1. Open `index.html` in your web browser
2. The application will load with default text elements

### Adding Background Images
1. Click "Choose File" under "Background Image"
2. Select an image file (JPEG, PNG, GIF, WebP)
3. The image will automatically fit to the canvas while maintaining aspect ratio
4. Use "Clear Image" to remove the background

### Editing Text
1. **Change Text**: Type in the input fields for Band Name, Address, and Time
2. **Move Text**: Click and drag any text element to reposition it
3. **Resize Text**: Use the size sliders to adjust font size (12px - 72px)
4. **Background Color**: Click the color picker to change text background color

### Text Elements
- **Band Name**: Default size 36px, positioned at top
- **Address**: Default size 24px, positioned in middle  
- **Time**: Default size 20px, positioned at bottom

### Exporting Your Flyer
1. Click "Export as PNG" to download your flyer
2. The file will be saved as `flyer-[timestamp].png`

### Saving and Loading Projects
1. **Save Project**: Click "Save Project" to download a `.json` file with all your settings
2. **Load Project**: Click "Load Project" and select a previously saved `.json` file
3. Projects include text content, positions, sizes, colors, and background images

## Technical Details

### File Structure
- `index.html` - Main application interface
- `style.css` - Styling and responsive design
- `app.js` - Main application logic and initialization
- `canvas-manager.js` - Canvas rendering and interaction handling
- `text-element.js` - Text element class with drag/drop functionality
- `file-handler.js` - Image upload and project save/load functionality

### Browser Compatibility
- Modern browsers with HTML5 Canvas support
- Chrome, Firefox, Safari, Edge (latest versions)
- Mobile browsers with touch support

### Image Requirements
- Supported formats: JPEG, PNG, GIF, WebP
- Maximum file size: 10MB
- Images are automatically scaled to fit canvas

## Tips for Best Results

1. **Image Selection**: Choose high-contrast images that won't interfere with text readability
2. **Text Positioning**: Drag text to areas with good contrast against the background
3. **Background Colors**: Use dark backgrounds for light images, light backgrounds for dark images
4. **Font Sizes**: Larger text (36px+) works better for band names, smaller (20-24px) for details
5. **Save Projects**: Save your work frequently to avoid losing changes

## Keyboard Shortcuts

- **Ctrl/Cmd + S**: Save project (when focused on the page)
- **Ctrl/Cmd + E**: Export flyer (when focused on the page)

## Troubleshooting

### Image Won't Load
- Check file format (must be JPEG, PNG, GIF, or WebP)
- Ensure file size is under 10MB
- Try a different image file

### Text Not Dragging
- Make sure you're clicking directly on the text (not just near it)
- Try refreshing the page if interactions stop working

### Export Not Working
- Ensure your browser supports HTML5 Canvas downloads
- Try using a different browser
- Check that popup blockers aren't preventing the download

### Project Won't Load
- Verify the file is a valid JSON project file
- Check that the file wasn't corrupted during transfer
- Try loading a different project file

## Future Enhancements

Potential features for future versions:
- Multiple font families
- Text rotation and effects
- Layer management
- Undo/redo functionality
- Template library
- Batch export options

## Support

For issues or questions, check the browser console for error messages and ensure you're using a modern, up-to-date web browser.
