class FlyerApp {
    constructor() {
        this.canvasManager = null;
        this.fileHandler = null;
        this.textElements = [];
        
        this.init();
    }

    init() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        // Initialize canvas manager
        this.canvasManager = new CanvasManager('flyerCanvas');
        
        // Initialize file handler
        this.fileHandler = new FileHandler(this.canvasManager);
        
        // Create initial text elements
        this.createInitialTextElements();
        
        // Setup UI event listeners
        this.setupUIControls();
        
        console.log('Flyer App initialized successfully');
    }

    createInitialTextElements() {
        // Create three text elements with default positions
        const bandNameElement = new TextElement('The Rock Band', 50, 100, 36, '#000000', '#ffffff');
        const addressElement = new TextElement('123 Music St, City', 50, 200, 24, '#000000', '#ffffff');
        const timeElement = new TextElement('8:00 PM - Saturday', 50, 300, 20, '#000000', '#ffffff');
        
        this.textElements = [bandNameElement, addressElement, timeElement];
        
        // Add to canvas manager
        this.textElements.forEach(element => {
            this.canvasManager.addTextElement(element);
        });
    }

    setupUIControls() {
        // Band name controls
        this.setupTextControls('bandName', 0);
        this.setupTextControls('address', 1);
        this.setupTextControls('time', 2);
    }

    setupTextControls(elementId, textElementIndex) {
        // Text input
        const textInput = document.getElementById(elementId);
        textInput.addEventListener('input', (e) => {
            this.canvasManager.updateTextElement(textElementIndex, 'text', e.target.value);
        });

        // Background color
        const bgColorInput = document.getElementById(elementId + 'Bg');
        bgColorInput.addEventListener('change', (e) => {
            this.canvasManager.updateTextElement(textElementIndex, 'backgroundColor', e.target.value);
        });

        // Font size
        const sizeInput = document.getElementById(elementId + 'Size');
        const sizeDisplay = sizeInput.nextElementSibling;
        
        sizeInput.addEventListener('input', (e) => {
            const size = parseInt(e.target.value);
            this.canvasManager.updateTextElement(textElementIndex, 'fontSize', size);
            sizeDisplay.textContent = `${size}px`;
        });

        // Initialize size display
        sizeDisplay.textContent = `${sizeInput.value}px`;
    }

    // Public methods for external control
    updateBandName(text) {
        document.getElementById('bandName').value = text;
        this.canvasManager.updateTextElement(0, 'text', text);
    }

    updateAddress(text) {
        document.getElementById('address').value = text;
        this.canvasManager.updateTextElement(1, 'text', text);
    }

    updateTime(text) {
        document.getElementById('time').value = text;
        this.canvasManager.updateTextElement(2, 'text', text);
    }

    setBackgroundImage(imageFile) {
        if (imageFile && imageFile.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    this.canvasManager.setBackgroundImage(img);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(imageFile);
        }
    }

    exportFlyer() {
        return this.canvasManager.exportAsImage('png', 1.0);
    }

    reset() {
        // Reset canvas
        this.canvasManager.reset();
        
        // Reset form inputs
        document.getElementById('bandName').value = 'The Rock Band';
        document.getElementById('address').value = '123 Music St, City';
        document.getElementById('time').value = '8:00 PM - Saturday';
        document.getElementById('imageUpload').value = '';
        
        // Reset color inputs
        document.getElementById('bandNameBg').value = '#000000';
        document.getElementById('addressBg').value = '#000000';
        document.getElementById('timeBg').value = '#000000';
        
        // Reset size inputs
        document.getElementById('bandNameSize').value = 36;
        document.getElementById('addressSize').value = 24;
        document.getElementById('timeSize').value = 20;
        
        // Update size displays
        document.querySelector('#bandNameSize + .size-display').textContent = '36px';
        document.querySelector('#addressSize + .size-display').textContent = '24px';
        document.querySelector('#timeSize + .size-display').textContent = '20px';
        
        // Recreate initial text elements
        this.createInitialTextElements();
        
        console.log('App reset to initial state');
    }

    // Get current flyer data for saving
    getFlyerData() {
        return {
            bandName: document.getElementById('bandName').value,
            address: document.getElementById('address').value,
            time: document.getElementById('time').value,
            projectData: this.canvasManager.getProjectData()
        };
    }

    // Load flyer data
    loadFlyerData(data) {
        if (data.projectData) {
            this.fileHandler.loadProject(data.projectData);
        }
        
        // Update form fields
        if (data.bandName) this.updateBandName(data.bandName);
        if (data.address) this.updateAddress(data.address);
        if (data.time) this.updateTime(data.time);
    }
}

// Initialize the app when the script loads
const flyerApp = new FlyerApp();

// Make app globally accessible for debugging
window.flyerApp = flyerApp;
