class CanvasManager {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.backgroundImage = null;
        this.textElements = [];
        this.isDragging = false;
        this.dragElement = null;
        
        this.setupEventListeners();
        this.render();
    }

    setupEventListeners() {
        // Mouse events for dragging
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('mouseleave', (e) => this.handleMouseUp(e));

        // Touch events for mobile support
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
    }

    getMousePos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        return {
            x: (e.clientX - rect.left) * scaleX,
            y: (e.clientY - rect.top) * scaleY
        };
    }

    getTouchPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        const touch = e.touches[0] || e.changedTouches[0];
        return {
            x: (touch.clientX - rect.left) * scaleX,
            y: (touch.clientY - rect.top) * scaleY
        };
    }

    handleMouseDown(e) {
        const pos = this.getMousePos(e);
        this.startDrag(pos.x, pos.y);
    }

    handleMouseMove(e) {
        const pos = this.getMousePos(e);
        this.updateDrag(pos.x, pos.y);
    }

    handleMouseUp(e) {
        this.stopDrag();
    }

    handleTouchStart(e) {
        e.preventDefault();
        const pos = this.getTouchPos(e);
        this.startDrag(pos.x, pos.y);
    }

    handleTouchMove(e) {
        e.preventDefault();
        const pos = this.getTouchPos(e);
        this.updateDrag(pos.x, pos.y);
    }

    handleTouchEnd(e) {
        e.preventDefault();
        this.stopDrag();
    }

    startDrag(x, y) {
        // Check text elements in reverse order (top to bottom)
        for (let i = this.textElements.length - 1; i >= 0; i--) {
            const element = this.textElements[i];
            if (element.startDrag(x, y, this.ctx)) {
                this.isDragging = true;
                this.dragElement = element;
                this.canvas.classList.add('dragging');
                this.render();
                break;
            }
        }
    }

    updateDrag(x, y) {
        if (this.isDragging && this.dragElement) {
            this.dragElement.updateDrag(x, y, this.canvas.width, this.canvas.height, this.ctx);
            this.render();
        }
    }

    stopDrag() {
        if (this.isDragging) {
            this.isDragging = false;
            if (this.dragElement) {
                this.dragElement.stopDrag();
                this.dragElement = null;
            }
            this.canvas.classList.remove('dragging');
            this.render();
        }
    }

    setBackgroundImage(image) {
        this.backgroundImage = image;
        this.render();
    }

    clearBackgroundImage() {
        this.backgroundImage = null;
        this.render();
    }

    addTextElement(textElement) {
        this.textElements.push(textElement);
        this.render();
    }

    removeTextElement(textElement) {
        const index = this.textElements.indexOf(textElement);
        if (index > -1) {
            this.textElements.splice(index, 1);
            this.render();
        }
    }

    updateTextElement(index, property, value) {
        if (index >= 0 && index < this.textElements.length) {
            const element = this.textElements[index];
            switch (property) {
                case 'text':
                    element.updateText(value);
                    break;
                case 'fontSize':
                    element.updateFontSize(value);
                    break;
                case 'backgroundColor':
                    element.updateBackgroundColor(value);
                    break;
                case 'textColor':
                    element.updateTextColor(value);
                    break;
            }
            this.render();
        }
    }

    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background image if present
        if (this.backgroundImage) {
            this.drawBackgroundImage();
        }

        // Draw all text elements
        this.textElements.forEach(element => {
            element.render(this.ctx);
        });
    }

    drawBackgroundImage() {
        const img = this.backgroundImage;
        const canvasAspect = this.canvas.width / this.canvas.height;
        const imgAspect = img.width / img.height;

        let drawWidth, drawHeight, drawX, drawY;

        if (imgAspect > canvasAspect) {
            // Image is wider than canvas aspect ratio
            drawHeight = this.canvas.height;
            drawWidth = drawHeight * imgAspect;
            drawX = (this.canvas.width - drawWidth) / 2;
            drawY = 0;
        } else {
            // Image is taller than canvas aspect ratio
            drawWidth = this.canvas.width;
            drawHeight = drawWidth / imgAspect;
            drawX = 0;
            drawY = (this.canvas.height - drawHeight) / 2;
        }

        this.ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);
    }

    exportAsImage(format = 'png', quality = 0.9) {
        return this.canvas.toDataURL(`image/${format}`, quality);
    }

    getProjectData() {
        return {
            textElements: this.textElements.map(element => element.toJSON()),
            backgroundImageData: this.backgroundImage ? this.canvas.toDataURL() : null,
            canvasWidth: this.canvas.width,
            canvasHeight: this.canvas.height
        };
    }

    loadProjectData(data) {
        // Clear existing elements
        this.textElements = [];
        
        // Load text elements
        if (data.textElements) {
            data.textElements.forEach(elementData => {
                this.textElements.push(TextElement.fromJSON(elementData));
            });
        }

        // Load background image if present
        if (data.backgroundImageData) {
            const img = new Image();
            img.onload = () => {
                this.backgroundImage = img;
                this.render();
            };
            img.src = data.backgroundImageData;
        } else {
            this.backgroundImage = null;
        }

        this.render();
    }

    reset() {
        this.textElements = [];
        this.backgroundImage = null;
        this.render();
    }
}
