class TextElement {
    constructor(text, x, y, fontSize = 24, backgroundColor = '#000000', textColor = '#ffffff') {
        this.text = text;
        this.x = x;
        this.y = y;
        this.fontSize = fontSize;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
        this.isDragging = false;
        this.dragOffsetX = 0;
        this.dragOffsetY = 0;
        this.padding = 8;
        this.borderRadius = 4;
        
        // Bounds for collision detection
        this.width = 0;
        this.height = 0;
    }

    updateText(text) {
        this.text = text;
    }

    updateFontSize(fontSize) {
        this.fontSize = fontSize;
    }

    updateBackgroundColor(color) {
        this.backgroundColor = color;
    }

    updateTextColor(color) {
        this.textColor = color;
    }

    // Calculate text bounds for collision detection
    calculateBounds(ctx) {
        ctx.font = `${this.fontSize}px Arial, sans-serif`;
        const metrics = ctx.measureText(this.text);
        
        this.width = metrics.width + (this.padding * 2);
        this.height = this.fontSize + (this.padding * 2);
        
        return {
            x: this.x,
            y: this.y - this.fontSize,
            width: this.width,
            height: this.height
        };
    }

    // Check if point is inside text bounds
    containsPoint(x, y, ctx) {
        const bounds = this.calculateBounds(ctx);
        return x >= bounds.x && 
               x <= bounds.x + bounds.width && 
               y >= bounds.y && 
               y <= bounds.y + bounds.height;
    }

    // Start dragging
    startDrag(mouseX, mouseY, ctx) {
        if (this.containsPoint(mouseX, mouseY, ctx)) {
            this.isDragging = true;
            this.dragOffsetX = mouseX - this.x;
            this.dragOffsetY = mouseY - this.y;
            return true;
        }
        return false;
    }

    // Update position during drag
    updateDrag(mouseX, mouseY, canvasWidth, canvasHeight, ctx) {
        if (this.isDragging) {
            const newX = mouseX - this.dragOffsetX;
            const newY = mouseY - this.dragOffsetY;
            
            // Calculate bounds to keep text within canvas
            const bounds = this.calculateBounds(ctx);
            const minX = 0;
            const maxX = canvasWidth - bounds.width;
            const minY = this.fontSize;
            const maxY = canvasHeight;
            
            // Constrain position
            this.x = Math.max(minX, Math.min(maxX, newX));
            this.y = Math.max(minY, Math.min(maxY, newY));
        }
    }

    // Stop dragging
    stopDrag() {
        this.isDragging = false;
    }

    // Render the text element
    render(ctx) {
        if (!this.text.trim()) return;

        ctx.font = `${this.fontSize}px Arial, sans-serif`;
        const metrics = ctx.measureText(this.text);
        
        // Calculate background rectangle
        const bgX = this.x;
        const bgY = this.y - this.fontSize;
        const bgWidth = metrics.width + (this.padding * 2);
        const bgHeight = this.fontSize + (this.padding * 2);

        // Draw background with rounded corners
        ctx.fillStyle = this.backgroundColor;
        this.drawRoundedRect(ctx, bgX, bgY, bgWidth, bgHeight, this.borderRadius);
        ctx.fill();

        // Draw text
        ctx.fillStyle = this.textColor;
        ctx.textBaseline = 'top';
        ctx.fillText(this.text, this.x + this.padding, this.y - this.fontSize + this.padding);

        // Draw selection indicator if dragging
        if (this.isDragging) {
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            this.drawRoundedRect(ctx, bgX - 2, bgY - 2, bgWidth + 4, bgHeight + 4, this.borderRadius + 2);
            ctx.stroke();
            ctx.setLineDash([]);
        }
    }

    // Helper method to draw rounded rectangles
    drawRoundedRect(ctx, x, y, width, height, radius) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }

    // Get serializable data for saving
    toJSON() {
        return {
            text: this.text,
            x: this.x,
            y: this.y,
            fontSize: this.fontSize,
            backgroundColor: this.backgroundColor,
            textColor: this.textColor
        };
    }

    // Create from saved data
    static fromJSON(data) {
        return new TextElement(
            data.text,
            data.x,
            data.y,
            data.fontSize,
            data.backgroundColor,
            data.textColor
        );
    }
}
